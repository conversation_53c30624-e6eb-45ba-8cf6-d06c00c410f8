import 'package:dawraq/generated/l10n.dart';
import 'package:dawraq/src/core/services/app_settings/controller/settings_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class AppConsts {
  static const String appName = 'Dawraq';
  static bool isEnglish(WidgetRef ref) {
    final settingsController = ref.watch(settingsControllerProvider);
    final langCode = settingsController.locale.languageCode;
    return langCode == 'en';
  }

  static const Locale locale = Locale('ar');

  static const List<Locale> supportedLocales = [
    locale,
    Locale('en'),
  ];

  static const List<LocalizationsDelegate> localizationsDelegates = [
    S.delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  static const String testSchoolID = '148262';
  // static const String testSchoolID = 'admin';
  // static const String testEmail = '<EMAIL>';
  // static const String testEmail = '<EMAIL>';
  static const String testPass = '12345678';
}
