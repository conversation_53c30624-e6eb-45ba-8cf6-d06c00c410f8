import 'package:dawraq/src/features/content/models/content_model.dart';
import 'package:dawraq/src/features/content/repos/content.repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:xr_helper/xr_helper.dart';

class ContentController extends BaseController {
  final BuildContext context;
  final ContentRepo contentRepo;

  ContentController(
    this.context, {
    required this.contentRepo,
  });

  // * Get Main Content ================================
  Future<(bool isActive, List<ContentModel> content)> getMainContent({
    String? searchValue,
    int perPage = 10, // Default value
  }) async {
    return await baseFunction(context, () async {
      final data = await contentRepo.getMainContent(
        searchValue: searchValue,
        perPage: perPage,
      );

      return data;
    });
  }

  // Future<(bool isActive, List<ContentModel> content)> getMainContent({
  //   String? searchValue,
  // }) async {
  //   return await baseFunction(context, () async {
  //     final data = await contentRepo.getMainContent(
  //       searchValue: searchValue,
  //     );
  //
  //     return data;
  //   });
  // }

  // * Get Content Details ================================
  Future<ContentModel> getContentDetails(int id) async {
    return await baseFunction(context, () async {
      final data = await contentRepo.getContentDetails(id);

      return data;
    });
  }
}
