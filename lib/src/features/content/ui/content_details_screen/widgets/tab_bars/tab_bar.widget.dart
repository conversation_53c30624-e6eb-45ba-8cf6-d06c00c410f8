import 'package:dawraq/src/core/shared_widgets/base_tab_bar/base_tab_bar.dart';
import 'package:dawraq/src/core/theme/color_manager.dart';
import 'package:dawraq/src/features/auth/models/user_model_helper.dart';
import 'package:dawraq/src/features/content/models/content_model.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class DetailsTabBarWidget extends StatelessWidget {
  final ValueNotifier<int> tabIndex;
  final ValueNotifier<bool> isLoading;
  final ContentModel? content;

  const DetailsTabBarWidget({
    super.key,
    required this.tabIndex,
    required this.isLoading,
    this.content,
  });

  @override
  Widget build(BuildContext context) {
    if (!UserModelHelper.currentUser().isTeacher) {
      return const SizedBox.shrink();
    }

    List<Tab> tabs = [
      if (!kIsWeb) const Tab(text: 'المعلم'),
      const Tab(text: 'الصف'),
      const Tab(text: 'الطالب'),
      // Add AI Analysis tab if user has permission and data exists
      if (UserModelHelper.canViewAnalysis() &&
          content?.aiAnalysis != null &&
          content?.aiAnalysis?.analysis.isNotEmpty == true)
        const Tab(text: 'التحليل بالذكاء الاصطناعي'),
      // Add Questions tab if user has permission
      if (UserModelHelper.canViewQuestions())
        const Tab(text: 'الأسئلة والأجوبة'),
    ];

    Color indicatorColor;

    switch (tabIndex.value) {
      case 0:
        indicatorColor =
            kIsWeb ? ColorManager.classTabColor : ColorManager.teacherTabColor;
        break;
      case 1:
        indicatorColor =
            kIsWeb ? ColorManager.studentTabColor : ColorManager.classTabColor;
        break;
      case 2:
        indicatorColor = ColorManager.studentTabColor;
        break;
      default:
        // For AI tabs, use a different color
        indicatorColor = ColorManager.primaryColor;
    }

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(
          AppRadius.xxLargeRadius,
        ),
        border: Border.all(
          color: Colors.grey,
          width: 0.5,
        ),
      ),
      child: BaseTabBar(
        isScrollable: UserModelHelper.canViewAnalysis() &&
            content?.aiAnalysis != null &&
            content?.aiAnalysis?.analysis.isNotEmpty == true,
        dividerHeight: 0,
        indicatorColor: indicatorColor,
        tabs: tabs,
        onTap: (index) {
          isLoading.value = true;
          tabIndex.value = index;
          Future.delayed(const Duration(milliseconds: 200), () {
            isLoading.value = false;
          });
        },
        initialTabIndex: tabIndex.value,
      ),
    );
  }
}

// import 'package:dawraq/src/core/shared_widgets/base_tab_bar/base_tab_bar.dart';
// import 'package:dawraq/src/core/theme/color_manager.dart';
// import 'package:dawraq/src/features/auth/models/user_model_helper.dart';
// import 'package:flutter/material.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class DetailsTabBarWidget extends StatelessWidget {
//   final ValueNotifier<int> tabIndex;
//   final ValueNotifier<bool> isLoading;
//
//   const DetailsTabBarWidget({
//     super.key,
//     required this.tabIndex,
//     required this.isLoading,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     if (!UserModelHelper.currentUser().isTeacher) {
//       return const SizedBox.shrink();
//     }

//     Color indicatorColor;
//
//     switch (tabIndex.value) {
//       case 0:
//         indicatorColor = ColorManager.teacherTabColor;
//         break;
//       case 1:
//         indicatorColor = ColorManager.classTabColor;
//         break;
//       case 2:
//         indicatorColor = ColorManager.studentTabColor;
//         break;
//       default:
//         indicatorColor = ColorManager.classTabColor;
//     }
//
//     return Container(
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(
//           AppRadius.xxLargeRadius,
//         ),
//         border: Border.all(
//           color: Colors.grey,
//           width: 0.5,
//         ),
//       ),
//       child: BaseTabBar(
//         dividerHeight: 0,
//         indicatorColor: indicatorColor,
//         tabs: const [
//           Tab(
//             text: 'المعلم',
//           ),
//           Tab(
//             text: 'الصف',
//           ),
//           Tab(
//             text: 'الطالب',
//           ),
//         ],
//         onTap: (index) {
//           isLoading.value = true;
//           tabIndex.value = index;
//           Future.delayed(const Duration(milliseconds: 200), () {
//             isLoading.value = false;
//           });
//         },
//         initialTabIndex: 0,
//       ),
//     );
//   }
// }
