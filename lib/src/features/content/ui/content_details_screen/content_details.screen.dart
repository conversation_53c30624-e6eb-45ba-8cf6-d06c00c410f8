import 'package:dawraq/src/core/extensions/riverpod_extensions.dart';
import 'package:dawraq/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:dawraq/src/core/shared_widgets/loading/loading_widgets/home_loading/details_loading.dart';
import 'package:dawraq/src/core/theme/color_manager.dart';
import 'package:dawraq/src/features/auth/models/user_model_helper.dart';
import 'package:dawraq/src/features/content/models/content_model.dart';
import 'package:dawraq/src/features/content/providers/content.providers.dart';
import 'package:dawraq/src/features/content/ui/content_details_screen/widgets/base_details.widget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/ai_analysis.widget.dart';
import 'widgets/questions_answers.widget.dart';
import 'widgets/tab_bars/tab_bar.widget.dart';
import 'widgets/top_section.widget.dart';

class ContentDetailsScreen extends HookConsumerWidget {
  final ContentModel mainContent;
  final int? initialTabIndex;

  const ContentDetailsScreen({
    super.key,
    required this.mainContent,
    this.initialTabIndex,
  });

  // Helper method to calculate questions tab index
  static int getQuestionsTabIndex(ContentModel content) {
    int baseTabCount = kIsWeb ? 2 : 3;
    return baseTabCount +
        (UserModelHelper.canViewAnalysis() &&
                content.aiAnalysis != null &&
                content.aiAnalysis!.analysis.isNotEmpty
            ? 1
            : 0);
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final params = (context, mainContent.id!);

    final contentDetailsFuture =
        ref.watch(contentDetailsFutureProvider(params));

    final tabIndex = useState(
        initialTabIndex ?? (UserModelHelper.currentUser().isTeacher ? 0 : 2));

    final isLoading = useState(false);

    Color color;

    switch (tabIndex.value) {
      case 0:
        color = kIsWeb
            ? ColorManager.classPageColor
            : ColorManager.teacherPageColor;
        break;
      case 1:
        color = kIsWeb
            ? ColorManager.studentPageColor
            : ColorManager.classPageColor;
        break;
      case 2:
        color = ColorManager.studentPageColor;
        break;
      default:
        color = ColorManager.classTabColor;
    }

    return contentDetailsFuture.get(
      loading: () => DetailsLoading(title: mainContent.title),
      data: (data) {
        final content = data;

        Widget selectedWidget() {
          // Calculate tab indices dynamically
          int baseTabCount = kIsWeb ? 2 : 3;
          int aiAnalysisTabIndex = baseTabCount;
          int questionsTabIndex = baseTabCount +
              (UserModelHelper.canViewAnalysis() &&
                      content.aiAnalysis != null &&
                      content.aiAnalysis!.analysis.isNotEmpty
                  ? 1
                  : 0);

          if (kIsWeb) {
            if (tabIndex.value == 0) {
              return BaseDetailsWidget(
                content: content.contentClass,
                videos: content.classMedia,
                color: color,
              );
            } else if (tabIndex.value == 1) {
              return BaseDetailsWidget(
                content: content.contentStudent,
                videos: content.studentMedia,
                color: color,
              );
            }
          } else {
            if (tabIndex.value == 0) {
              return BaseDetailsWidget(
                content: content.contentTeacher,
                videos: content.teacherMedia,
                color: color,
              );
            } else if (tabIndex.value == 1) {
              return BaseDetailsWidget(
                content: content.contentClass,
                videos: content.classMedia,
                color: color,
              );
            } else if (tabIndex.value == 2) {
              return BaseDetailsWidget(
                content: content.contentStudent,
                videos: content.studentMedia,
                color: color,
              );
            }
          }

          // Handle AI Analysis tab
          if (UserModelHelper.canViewAnalysis() &&
              content.aiAnalysis != null &&
              content.aiAnalysis!.analysis.isNotEmpty &&
              tabIndex.value == aiAnalysisTabIndex) {
            return AIAnalysisWidget(
              aiAnalysis: content.aiAnalysis,
              color: color,
            );
          }

          // Handle Questions tab
          if (UserModelHelper.canViewQuestions() &&
              tabIndex.value == questionsTabIndex) {
            return QuestionsAnswersWidget(
              questionsAnswers: content.questionsAnswers,
              color: color,
              content: content,
            );
          }

          // Default fallback
          return BaseDetailsWidget(
            content: content.contentStudent,
            videos: content.studentMedia,
            color: color,
          );
        }

        return Scaffold(
          backgroundColor: color,
          body: Column(
            children: [
              AppGaps.largeGap,
              TopSectionWidget(
                title: mainContent.title,
              ),
              AppGaps.mediumGap,
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    color: ColorManager.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(AppRadius.xLargeRadius),
                      topRight: Radius.circular(AppRadius.xLargeRadius),
                    ),
                  ),
                  child: Column(
                    children: [
                      if (UserModelHelper.currentUser().isTeacher)
                        Padding(
                          padding:
                              const EdgeInsets.all(AppSpaces.mediumPadding),
                          child: DetailsTabBarWidget(
                            tabIndex: tabIndex,
                            isLoading: isLoading,
                            content: content,
                          ),
                        )
                      else
                        AppGaps.mediumGap,
                      if (isLoading.value)
                        const Expanded(child: LoadingWidget())
                      else
                        Expanded(
                          child: selectedWidget(),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// class ContentDetailsScreen extends HookConsumerWidget {
//   final ContentModel mainContent;
//
//   const ContentDetailsScreen({super.key, required this.mainContent});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final params = (context, mainContent.id!);
//
//     final contentDetailsFuture =
//         ref.watch(contentDetailsFutureProvider(params));
//
//     final tabIndex = useState(UserModelHelper.currentUser().isTeacher ? 0 : 2);
//     final isLoading = useState(false);
//
//     Color color;
//
//     switch (tabIndex.value) {
//       case 0:
//         color = ColorManager.teacherPageColor;
//         break;
//       case 1:
//         color = ColorManager.classPageColor;
//         break;
//       case 2:
//         color = ColorManager.studentPageColor;
//         break;
//       default:
//         color = ColorManager.classTabColor;
//     }
//
//     return contentDetailsFuture.get(
//       loading: () => DetailsLoading(title: mainContent.title),
//       data: (data) {
//         final content = data;
//
//         Widget selectedWidget() {
//           if (tabIndex.value == 0) {
//             return BaseDetailsWidget(
//               content: content.contentTeacher,
//               videos: content.teacherMedia,
//               color: color,
//             );
//           } else if (tabIndex.value == 1) {
//             return BaseDetailsWidget(
//               content: content.contentClass,
//               videos: content.classMedia,
//               color: color,
//             );
//           } else {
//             return BaseDetailsWidget(
//               content: content.contentStudent,
//               videos: content.studentMedia,
//               color: color,
//             );
//           }
//         }
//
//         return Scaffold(
//           backgroundColor: color,
//           body: Column(
//             children: [
//               AppGaps.largeGap,
//               TopSectionWidget(
//                 title: mainContent.title,
//               ),
//               AppGaps.mediumGap,
//               Expanded(
//                 child: Container(
//                   width: double.infinity,
//                   decoration: const BoxDecoration(
//                     color: ColorManager.white,
//                     borderRadius: BorderRadius.only(
//                       topLeft: Radius.circular(AppRadius.xLargeRadius),
//                       topRight: Radius.circular(AppRadius.xLargeRadius),
//                     ),
//                   ),
//                   child: Column(
//                     children: [
//                       if (UserModelHelper.currentUser().isTeacher)
//                         Padding(
//                           padding:
//                               const EdgeInsets.all(AppSpaces.mediumPadding),
//                           child: DetailsTabBarWidget(
//                             tabIndex: tabIndex,
//                             isLoading: isLoading,
//                           ),
//                         )
//                       else
//                         AppGaps.mediumGap,
//                       if (isLoading.value)
//                         const Expanded(child: LoadingWidget())
//                       else
//                         Expanded(
//                           child: selectedWidget(),
//                         ),
//                     ],
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         );
//       },
//     );
//   }
// }
