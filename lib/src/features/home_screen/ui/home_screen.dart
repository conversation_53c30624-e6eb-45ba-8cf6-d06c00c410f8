import 'dart:developer';
import 'dart:io';

import 'package:dawraq/src/base_app.dart';
import 'package:dawraq/src/core/extensions/context_extensions.dart';
import 'package:dawraq/src/core/shared_widgets/base_mobile_view_widget/base_mobile_view.widget.dart';
import 'package:dawraq/src/core/shared_widgets/dialog/show_base_dialog.dart';
import 'package:dawraq/src/core/shared_widgets/loading/loading_widgets/home_loading/cards_loading.dart';
import 'package:dawraq/src/features/auth/ui/login_screen.dart';
import 'package:dawraq/src/features/content/models/content_model.dart';
import 'package:dawraq/src/features/content/providers/content.providers.dart';
import 'package:dawraq/src/features/content/ui/widgets/content_card.dart';
import 'package:dawraq/src/features/home_screen/ui/widgets/drawer_widget.dart';
import 'package:dawraq/src/features/home_screen/ui/widgets/search_bar.widget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/empty_widget.dart';
import 'widgets/home_appbar.widget.dart';

class HomeScreen extends HookConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchUseState = useState<String>('');

    return ValueListenableBuilder(
        valueListenable: searchUseState,
        builder: (context, searchValue, child) {
          return HookConsumer(builder: (context, ref, child) {
            final isWebMobile = kIsWeb &&
                (defaultTargetPlatform == TargetPlatform.iOS ||
                    defaultTargetPlatform == TargetPlatform.android);

            final user = GetStorageService.getLocalData(key: LocalKeys.user);

            final isStudent = user['type'] == 'Student';

            if (isWebMobile && !isStudent) {
              return const CannotOpenFromWebMobile();
            }

            final perPage = useState<int>(10);
            final isLoadingMore = useState<bool>(false);
            final isInitialLoadComplete = useState<bool>(false);
            final previousSearchValue = useState<String>('');

            final params = (context, searchValue, perPage.value);

            final mainContent = useState<List<ContentModel>>([]);
            final isActive = useState<bool>(true);

            final scrollController = useScrollController();

            ref.listen(mainContentFutureProvider(params), (previous, next) {
              next.when(
                data: (data) {
                  isActive.value = data.$1;

                  final newContent = data.$2;

                  // Check if this is a search operation (search value changed)
                  final isSearchOperation =
                      previousSearchValue.value != searchValue;

                  if (isSearchOperation || previousSearchValue.value.isEmpty) {
                    // For search: replace content and reset pagination
                    mainContent.value = newContent;
                    perPage.value = 10;
                    previousSearchValue.value = searchValue;
                  } else {
                    // For pagination: append content
                    final combinedContent = [
                      ...mainContent.value,
                      ...newContent
                    ];
                    mainContent.value = combinedContent.toSet().toList();
                  }

                  isLoadingMore.value = false;

                  if (!isInitialLoadComplete.value) {
                    isInitialLoadComplete.value = true;
                  }
                },
                error: (error, stack) {
                  isLoadingMore.value = false;
                },
                loading: () {},
              );
            });

            useEffect(() {
              WidgetsBinding.instance!.addPostFrameCallback((_) {
                if (!isActive.value) {
                  context.toReplacement(const LoginScreen());
                  context.showBarMessage(context.tr.schoolIsNotActive,
                      isError: true);
                }
              });

              return () {};
            }, [mainContentFutureProvider, searchValue, perPage.value]);

            return MobileDesignWidget(
              child: WillPopScope(
                onWillPop: () async {
                  showBaseDialog(
                    context,
                    title: context.tr.exitApp,
                    content: context.tr.areYouSureYouWantToExitApp,
                    isDelete: true,
                    onConfirm: () {
                      exit(0);
                    },
                  );

                  return false;
                },
                child: Stack(
                  children: [
                    Scaffold(
                      drawer: const DrawerWidget(),
                      appBar: const HomeAppBar(),
                      body: SafeArea(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: AppSpaces.mediumPadding),
                          child: Column(
                            children: [
                              //! Search
                              SearchBarWidget(
                                searchValue: searchUseState,
                              ),

                              AppGaps.largeGap,

                              if (!isInitialLoadComplete.value)
                                const CardsLoading()
                              else
                                Expanded(
                                  child:
                                      NotificationListener<ScrollNotification>(
                                    onNotification:
                                        (ScrollNotification scrollInfo) {
                                      if (!isLoadingMore.value &&
                                          scrollInfo.metrics.pixels ==
                                              scrollInfo
                                                  .metrics.maxScrollExtent) {
                                        isLoadingMore.value = true;
                                        perPage.value += 10;
                                      }
                                      return true;
                                    },
                                    child: BaseList(
                                      controller: scrollController,
                                      emptyWidget: const EmptyContentWidget(),
                                      padding: const EdgeInsets.only(
                                          bottom: AppSpaces.smallPadding),
                                      data: mainContent.value,
                                      itemBuilder: (data, index) {
                                        final content = data;

                                        return ContentCard(content: content);
                                      },
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    if (isLoadingMore.value)
                      const Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: LinearProgressIndicator(),
                      ),
                  ],
                ),
              ),
            );
          });
        });
  }
}
