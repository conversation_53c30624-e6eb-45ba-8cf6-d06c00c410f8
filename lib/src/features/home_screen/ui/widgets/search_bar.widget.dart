import 'package:dawraq/src/core/extensions/context_extensions.dart';
import 'package:dawraq/src/core/theme/color_manager.dart';
import 'package:dawraq/src/core/theme/font_styles.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class SearchBarWidget extends StatelessWidget {
  final ValueNotifier<String> searchValue;

  const SearchBarWidget({super.key, required this.searchValue});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ColorManager.lightGrey,
        borderRadius: BorderRadius.circular(AppRadius.xLargeRadius),
      ),
      child: BaseTextField(
        onChanged: (value) {
          searchValue.value = value;
        },
        decoration: InputDecoration(
          icon: const Padding(
            padding: EdgeInsets.only(right: 16),
            child: Icon(CupertinoIcons.search),
          ),
          hintText: context.tr.search,
          hintStyle: AppTextStyles.body,
          contentPadding: const EdgeInsets.all(AppSpaces.mediumPadding),
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
        ),
      ),
    );
  }
}
