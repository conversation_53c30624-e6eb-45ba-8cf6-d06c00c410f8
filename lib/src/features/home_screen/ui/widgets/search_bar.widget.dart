import 'package:dawraq/src/core/extensions/context_extensions.dart';
import 'package:dawraq/src/core/theme/color_manager.dart';
import 'package:dawraq/src/core/theme/font_styles.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

class SearchBarWidget extends HookWidget {
  final ValueNotifier<String> searchValue;

  const SearchBarWidget({super.key, required this.searchValue});

  @override
  Widget build(BuildContext context) {
    final textController = useTextEditingController();
    final focusNode = useFocusNode();

    // Listen to searchValue changes to update the text controller
    useEffect(() {
      if (searchValue.value.isEmpty && textController.text.isNotEmpty) {
        textController.clear();
      }
      return null;
    }, [searchValue.value]);

    return Container(
      decoration: BoxDecoration(
        color: ColorManager.lightGrey,
        borderRadius: BorderRadius.circular(AppRadius.xLargeRadius),
      ),
      child: ValueListenableBuilder<String>(
        valueListenable: searchValue,
        builder: (context, currentSearchValue, child) {
          return BaseTextField(
            controller: textController,
            focusNode: focusNode,
            onChanged: (value) {
              searchValue.value = value;
            },
            onSubmitted: (value) {
              // Force refresh even if empty
              searchValue.value = value.trim();
              // Trigger a refresh by temporarily changing the value
              if (value.trim().isEmpty) {
                final temp = searchValue.value;
                searchValue.value = ' '; // Temporary different value
                Future.microtask(() => searchValue.value = temp);
              }
            },
            decoration: InputDecoration(
              prefixIcon: const Padding(
                padding: EdgeInsets.only(left: 16, right: 8),
                child: Icon(CupertinoIcons.search),
              ),
              suffixIcon: currentSearchValue.isNotEmpty
                  ? Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: IconButton(
                        icon: const Icon(
                          CupertinoIcons.clear_circled_solid,
                          color: Colors.grey,
                        ),
                        onPressed: () {
                          textController.clear();
                          searchValue.value = '';
                          focusNode.unfocus();
                        },
                      ),
                    )
                  : null,
              hintText: context.tr.search,
              hintStyle: AppTextStyles.body,
              contentPadding: const EdgeInsets.all(AppSpaces.mediumPadding),
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
            ),
          );
        },
      ),
    );
  }
}
